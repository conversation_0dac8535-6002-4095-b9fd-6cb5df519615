/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_AUTH_SERVICE_URL: string
  readonly VITE_GAME_SERVICE_URL: string
  readonly VITE_STORY_SERVICE_URL: string
  readonly VITE_SAVE_SERVICE_URL: string
  readonly VITE_AI_SERVICE_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_DEV_MODE: string
  readonly VITE_ENABLE_DEVTOOLS: string
  readonly VITE_LOG_LEVEL: string
  readonly VITE_AUTO_SAVE_INTERVAL: string
  readonly VITE_MAX_SAVE_SLOTS: string
  readonly VITE_ENABLE_SOUND: string
  readonly VITE_ENABLE_ANIMATIONS: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
