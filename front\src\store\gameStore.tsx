import { create } from 'zustand'
import { WorldMap, WorldSummary } from '../shared/types/World'

interface GameSettings {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: 'easy' | 'normal' | 'hard'
  autoSave: boolean
}

interface GameStoreState {
  worldSummaries: WorldSummary[]
  currentWorld: WorldMap | null
  currentWorldId: string | null
  settings: GameSettings
  setWorldSummaries: (worlds: WorldSummary[]) => void
  setCurrentWorld: (world: WorldMap | null) => void
  setCurrentWorldId: (worldId: string | null) => void
  updateSettings: (newSettings: Partial<GameSettings>) => void
  loadFullWorld: (worldId: string, userId: string) => Promise<void>
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameStoreState>((set, get) => ({
  worldSummaries: [],
  currentWorld: null,
  currentWorldId: null,
  settings: defaultSettings,
  setWorldSummaries: (worlds: WorldSummary[]) => set({ worldSummaries: worlds }),
  setCurrentWorld: (world: WorldMap | null) => set({ currentWorld: world }),
  setCurrentWorldId: (worldId: string | null) => set({ currentWorldId: worldId }),
  updateSettings: (newSettings: Partial<GameSettings>) =>
    set((state) => ({
      settings: { ...state.settings, ...newSettings }
    })),
  loadFullWorld: async (worldId: string, userId: string) => {
    try {
      const { getWorldById } = await import('../api/worldsApi')
      const result = await getWorldById(worldId, userId)
      if (result.success && result.world) {
        set({
          currentWorld: result.world,
          currentWorldId: worldId
        })
      }
    } catch (error) {
      console.error('Failed to load world:', error)
    }
  }
}))
