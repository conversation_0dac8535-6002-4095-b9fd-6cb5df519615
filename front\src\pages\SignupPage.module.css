.signupPage {
  padding: 4rem 1rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  width: 100%;
  max-width: 400px;
}

.card {
  background-color: var(--dark-card);
  padding: 2rem;
  border-radius: 0.5rem;
  border: 1px solid var(--dark-border);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.title {
  font-size: 1.875rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
  color: var(--nuclear-green);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.input {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--dark-bg);
  border: 1px solid var(--dark-border);
  border-radius: 0.375rem;
  color: var(--text-primary);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: var(--nuclear-green);
  box-shadow: 0 0 0 2px rgba(0, 255, 65, 0.2);
}

.input::placeholder {
  color: var(--text-muted);
}

.error {
  color: var(--nuclear-red);
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 0.375rem;
  border: 1px solid var(--nuclear-red);
}

.submitButton {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--nuclear-green), #00cc33);
  color: var(--dark-bg);
  font-weight: 600;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 1rem;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #00cc33, var(--nuclear-green));
  box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
  transform: translateY(-1px);
}

.submitButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.footer {
  text-align: center;
  margin-top: 1.5rem;
}

.footerText {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.footerLink {
  color: var(--nuclear-green);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footerLink:hover {
  color: var(--nuclear-yellow);
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 480px) {
  .signupPage {
    padding: 2rem 1rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .input {
    padding: 0.625rem;
  }
  
  .submitButton {
    padding: 0.625rem;
  }
}
