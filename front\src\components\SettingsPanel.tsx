import React, { useState } from 'react'
import { Volume2, VolumeX, Music, Speaker, Save, RotateCcw, User, Lock, Edit } from 'lucide-react'
import { useGameStore } from '../store/gameStore'
import { useAuthStore } from '../store/authStore'
import { usersApi } from '../api/usersApi'
import styles from './SettingsPanel.module.css'

type TabType = 'game' | 'account'

const SettingsPanel: React.FC = () => {
  const { settings, updateSettings } = useGameStore()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState<TabType>('game')
  const [accountForm, setAccountForm] = useState({
    username: user?.username || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const handleVolumeChange = (value: number) => {
    updateSettings({ volume: value / 100 })
  }

  const handleSoundToggle = () => {
    updateSettings({ soundEnabled: !settings.soundEnabled })
  }

  const handleMusicToggle = () => {
    updateSettings({ musicEnabled: !settings.musicEnabled })
  }


  const resetToDefaults = () => {
    if (confirm('Сбросить все настройки к значениям по умолчанию?')) {
      updateSettings({
        volume: 0.7,
        soundEnabled: true,
        musicEnabled: true,
        difficulty: 'normal',
        autoSave: true
      })
    }
  }

  const handleAccountFormChange = (field: keyof typeof accountForm, value: string) => {
    setAccountForm(prev => ({ ...prev, [field]: value }))
  }

  const handleUpdateProfile = async () => {
    try {
      // Check if username changed
      if (accountForm.username === user?.username) {
        alert('Нет изменений для сохранения')
        return
      }

      if (!accountForm.username.trim()) {
        alert('Имя пользователя не может быть пустым')
        return
      }

      await usersApi.updateProfile({ username: accountForm.username })
      
      // Update user in auth store
      const { updateUser } = useAuthStore.getState()
      updateUser({ username: accountForm.username })
      
      alert('Имя пользователя успешно обновлено!')
    } catch (error) {
      alert('Ошибка при обновлении профиля: ' + (error as Error).message)
    }
  }

  const handleChangePassword = async () => {
    if (accountForm.newPassword !== accountForm.confirmPassword) {
      alert('Новые пароли не совпадают')
      return
    }

    if (!accountForm.currentPassword || !accountForm.newPassword) {
      alert('Заполните все поля для смены пароля')
      return
    }

    try {
      await usersApi.changePassword({
        currentPassword: accountForm.currentPassword,
        newPassword: accountForm.newPassword
      })
      
      // Clear password fields
      setAccountForm(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }))
      
      alert('Пароль успешно изменен!')
    } catch (error) {
      alert('Ошибка при смене пароля: ' + (error as Error).message)
    }
  }

  return (
    <div className={styles.settingsPanel}>
      {/* Tabs */}
      <div className={styles.tabs}>
        <button
          onClick={() => setActiveTab('game')}
          className={`${styles.tab} ${activeTab === 'game' ? styles.tabActive : ''}`}
        >
          🎮 Игровые настройки
        </button>
        <button
          onClick={() => setActiveTab('account')}
          className={`${styles.tab} ${activeTab === 'account' ? styles.tabActive : ''}`}
        >
          👤 Аккаунт
        </button>
      </div>

      {/* Game Settings Tab */}
      {activeTab === 'game' && (
        <>
          {/* Audio Settings */}
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>
              🔊 Аудио настройки
            </h3>

            {/* Master Volume */}
            <div className={styles.setting}>
              <div className={styles.volumeContainer}>
                <label className={styles.labelText}>Общая громкость</label>
                <span className={styles.volumeValue}>{Math.round(settings.volume * 100)}%</span>
              </div>
              <div className={styles.sliderContainer}>
                <VolumeX />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={Math.round(settings.volume * 100)}
                  onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                  className={styles.slider}
                />
                <Volume2 />
              </div>
            </div>

            {/* Sound Effects Toggle */}
            <div className={styles.settingRow}>
              <div className={styles.settingLabel}>
                <Speaker />
                <label className={styles.labelText}>Звуковые эффекты</label>
              </div>
              <button
                onClick={handleSoundToggle}
                className={`${styles.toggle} ${settings.soundEnabled ? styles.toggleOn : styles.toggleOff}`}
              >
                <span
                  className={`${styles.toggleThumb} ${settings.soundEnabled ? styles.toggleThumbOn : ''}`}
                />
              </button>
            </div>

            {/* Music Toggle */}
            <div className={styles.settingRow}>
              <div className={styles.settingLabel}>
                <Music />
                <label className={styles.labelText}>Фоновая музыка</label>
              </div>
              <button
                onClick={handleMusicToggle}
                className={`${styles.toggle} ${settings.musicEnabled ? styles.toggleOn : styles.toggleOff}`}
              >
                <span
                  className={`${styles.toggleThumb} ${settings.musicEnabled ? styles.toggleThumbOn : ''}`}
                />
              </button>
            </div>
          </div>

         

          {/* Reset Button */}
          <div className={styles.resetSection}>
            <button
              onClick={resetToDefaults}
              className={styles.resetButton}
            >
              <RotateCcw />
              Сбросить к настройкам по умолчанию
            </button>
          </div>
        </>
      )}

      {/* Account Settings Tab */}
      {activeTab === 'account' && (
        <>
          {/* Profile Settings */}
          <div className={styles.profileSection}>
            <h3>
              👤 Профиль
            </h3>

            <div className={styles.inputGroup}>
              <label>Имя пользователя</label>
              <div className={styles.inputWrapper}>
                <User className={styles.inputIcon} />
                <input
                  type="text"
                  value={accountForm.username}
                  onChange={(e) => handleAccountFormChange('username', e.target.value)}
                  className={styles.input}
                  placeholder="Введите имя пользователя"
                />
              </div>
            </div>

            <button
              onClick={handleUpdateProfile}
              className={styles.saveButton}
            >
              <Edit />
              Обновить имя пользователя
            </button>
          </div>

          {/* Password Settings */}
          <div className={styles.passwordSection}>
            <h3>
              🔒 Безопасность
            </h3>

            <div className={styles.inputGroup}>
              <label>Текущий пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.currentPassword}
                  onChange={(e) => handleAccountFormChange('currentPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Введите текущий пароль"
                />
              </div>
            </div>

            <div className={styles.inputGroup}>
              <label>Новый пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.newPassword}
                  onChange={(e) => handleAccountFormChange('newPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Введите новый пароль"
                />
              </div>
            </div>

            <div className={styles.inputGroup}>
              <label>Подтвердите пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.confirmPassword}
                  onChange={(e) => handleAccountFormChange('confirmPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Подтвердите новый пароль"
                />
              </div>
            </div>

            <button
              onClick={handleChangePassword}
              className={styles.saveButton}
              disabled={!accountForm.currentPassword || !accountForm.newPassword || !accountForm.confirmPassword}
            >
              <Lock />
              Изменить пароль
            </button>
          </div>
        </>
      )}
    </div>
  )
}

export default SettingsPanel
