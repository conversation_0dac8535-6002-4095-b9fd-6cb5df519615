

export const IMCOMP_MAX_WIDTH = 1920
export const IMCOMP_MAX_HEIGHT = 990
/**
 * Константы для изометрической карты
 */

// Константы для изометрической проекции
export const BASE_TILE_WIDTH = 70
export const BASE_TILE_HEIGHT = 46

// Настройки рендеринга
export const TARGET_FPS = 60
export const FRAME_DURATION = 1000 / TARGET_FPS

// Настройки камеры
export const CAMERA_MOVE_SPEED = 10

// Настройки зума
export const MIN_ZOOM = 1
export const MAX_ZOOM = 2
export const ZOOM_STEP = 0.1

// Настройки тайлов
export const TILE_GAP = -0.5 // размер отступа между тайлами

// Настройки UI обновления
export const CAMERA_UI_UPDATE_INTERVAL = 10 // мс

// Пути к ресурсам
export const FOG_OF_WAR_TEXTURES = [
  `/textures/worldMap/fogOfWar/fogOfWar-1.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-2.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-3.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-4.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-5.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-6.jpg`
]
// Пути к текстурам местности
export const TERRAIN_TEXTURES = {
  grass: [
    `/textures/worldMap/terrain/grass-1.webp`,
    `/textures/worldMap/terrain/grass-2.webp`,
    `/textures/worldMap/terrain/grass-3.webp`,
    `/textures/worldMap/terrain/grass-4.webp`
  ],
  deadforest: [
    `/textures/worldMap/terrain/dead_forest-1.webp`,
    `/textures/worldMap/terrain/dead_forest-2.webp`,
    `/textures/worldMap/terrain/dead_forest-3.webp`,
    `/textures/worldMap/terrain/dead_forest-4.webp`
  ],
  mountain: [
    `/textures/worldMap/terrain/mountain-1.webp`,
    `/textures/worldMap/terrain/mountain-2.webp`,
    `/textures/worldMap/terrain/mountain-3.webp`,
    `/textures/worldMap/terrain/mountain-4.webp`
  ],
  water: [
    `/textures/worldMap/terrain/water-1.webp`,
    `/textures/worldMap/terrain/water-2.webp`,
    `/textures/worldMap/terrain/water-3.webp`,
    `/textures/worldMap/terrain/water-4.webp`
  ],
  desert: [
    `/textures/worldMap/terrain/desert-1.jpg`,
    `/textures/worldMap/terrain/desert-2.jpg`,
    `/textures/worldMap/terrain/desert-3.jpg`,
    `/textures/worldMap/terrain/desert-4.jpg`
  ],
  swamp: [
    `/textures/worldMap/terrain/swamp-1.webp`,
    `/textures/worldMap/terrain/swamp-2.webp`,
    `/textures/worldMap/terrain/swamp-3.webp`,
    `/textures/worldMap/terrain/swamp-4.webp`
  ],
  wasteland: [
    `/textures/worldMap/terrain/wasteland-1.webp`,
    `/textures/worldMap/terrain/wasteland-2.webp`,
    `/textures/worldMap/terrain/wasteland-3.webp`,
    `/textures/worldMap/terrain/wasteland-4.webp`
  ]
} as const

export const PLAYER_WM_TEXTURES = [
  `/textures/worldMap/player/WMPlayer-1.png`,
  `/textures/worldMap/player/WMPlayer-2.png`,
  `/textures/worldMap/player/WMPlayer-3.png`,
  `/textures/worldMap/player/WMPlayer-4.png`
].map((src) => {
  const img = new Image();
  img.src = src;
  return img;
});



// Настройки анимации тумана войны
export const FOG_ANIMATION_SPEED = 0.02// Скорость анимации (0.01 = медленно, 0.05 = быстро)
export const FOG_TRANSITION_DURATION = 5100 // Длительность перехода между состояниями в мс
export const FOG_IMAGE_TRANSITION_DURATION = 500 // Длительность анимации одного изображения в мс
export const FOG_FLICKER_PERIOD = 500 // Период моргания тумана войны (мс)
export const FOG_FLICKER_INTENSITY = 0.15 // Интенсивность мерцания (0.1 = слабо, 0.3 = сильно)
