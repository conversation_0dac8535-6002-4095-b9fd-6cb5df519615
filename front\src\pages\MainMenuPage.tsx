import React from 'react'
import { useNavigate } from 'react-router-dom'
import MainMenu from '../components/MainMenu'
import { useGameStore } from '../store/gameStore'
import { useAuthStore } from '../store/authStore'

const MainMenuPage: React.FC = () => {
  const navigate = useNavigate()
  const { loadFullWorld } = useGameStore()
  const { user } = useAuthStore()

  const handleStartGame = async (worldId: string) => {
    if (!user?.id) return

    try {
      // Загружаем полный мир
      await loadFullWorld(worldId, user.id)
      navigate('/game')
    } catch (error) {
      console.error('Failed to start game:', error)
    }
  }

  return <MainMenu onStartGame={handleStartGame} />
}

export default MainMenuPage
