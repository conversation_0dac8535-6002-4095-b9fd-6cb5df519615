/**
 * Демонстрационные утилиты для работы с текстурами местности
 * Используйте эти функции для быстрого тестирования и отладки
 */

import { TerrainType } from '../../../shared/enums'
import { drawTerrainTexture, terrainTextureManager } from './renderUtils'

/**
 * Демонстрационная функция - отрисовывает все типы местности в сетке
 * Полезно для тестирования загрузки и отображения текстур
 */
export const drawTerrainDemo = (
  ctx: CanvasRenderingContext2D,
  startX: number = 50,
  startY: number = 50,
  tileSize: number = 100
) => {
  const terrainTypes = [
    TerrainType.GRASS,
    TerrainType.DEADFOREST,
    TerrainType.MOUNTAIN,
    TerrainType.WATER,
    TerrainType.DESERT,
    TerrainType.SWAMP,
    TerrainType.WASTELAND
  ]

  const cols = 4
  const spacing = tileSize + 20

  terrainTypes.forEach((terrain, index) => {
    const col = index % cols
    const row = Math.floor(index / cols)
    
    const x = startX + col * spacing
    const y = startY + row * spacing
    
    // Отрисовываем ромбовидный тайл с текстурой
    drawTerrainTexture(
      ctx,
      x,
      y,
      tileSize / 2,
      tileSize / 3,
      terrain,
      0, // Первая вариация
      0  // Без поворота
    )

    // Подписываем тип местности
    ctx.fillStyle = '#000'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(terrain, x, y + tileSize / 2 + 20)
  })
}

/**
 * Простая функция для отрисовки одного тайла с текстурой
 * Удобна для быстрого тестирования
 */
export const drawSingleTerrainTile = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  terrain: TerrainType,
  variation: number = 0,
  rotation: number = 0
) => {
  drawTerrainTexture(
    ctx,
    x,
    y,
    width / 2,
    height / 2,
    terrain,
    variation,
    rotation
  )
}

/**
 * Проверяет статус загрузки всех текстур местности
 */
export const checkTextureLoadingStatus = (): Record<string, boolean> => {
  const terrainTypes = [
    TerrainType.GRASS,
    TerrainType.DEADFOREST,
    TerrainType.MOUNTAIN,
    TerrainType.WATER,
    TerrainType.DESERT,
    TerrainType.SWAMP,
    TerrainType.WASTELAND
  ]

  const status: Record<string, boolean> = {}
  
  terrainTypes.forEach(terrain => {
    const textures = terrainTextureManager.getLoadedTextures(terrain)
    status[terrain] = textures !== null && textures.length > 0
  })

  return status
}

/**
 * Выводит в консоль информацию о загруженных текстурах
 */
export const logTextureStatus = () => {
  const status = checkTextureLoadingStatus()
  console.log('🎨 Terrain Texture Loading Status:')
  
  Object.entries(status).forEach(([terrain, loaded]) => {
    const icon = loaded ? '✅' : '❌'
    console.log(`${icon} ${terrain}: ${loaded ? 'Loaded' : 'Not loaded'}`)
  })
}
