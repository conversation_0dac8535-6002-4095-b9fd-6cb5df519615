/**
 * Обработчики событий для изометрической карты
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { screenToIso, isoToScreen, getTileCenterOnScreen, isPointInDiamond } from './coordinateUtils';
import { CAMERA_MOVE_SPEED, MIN_ZOOM, MAX_ZOOM, ZOOM_STEP, TILE_GAP } from './constants';
// ...existing code...



export interface CameraRef {
  x: number;
  y: number;
}

/**
 * Создает обработчик движения мыши для перемещения камеры
 */
export const createMouseMoveHandler = (cameraRef: React.RefObject<CameraRef>) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    // Перетаскивание только правой кнопкой мыши (e.buttons === 2)
    if (e.buttons === 2 && cameraRef.current) {
      cameraRef.current.x -= e.movementX;
      cameraRef.current.y -= e.movementY;
    }
  };
};

/**
 * Создает обработчик изменения зума
 */
export const createZoomChangeHandler = (setZoom: (zoom: number) => void) => {
  return (newZoom: number) => {
    setZoom(Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, newZoom)));
  };
};

/**
 * Создает обработчик колеса мыши для зума
 */
export const createWheelHandler = (zoom: number, handleZoomChange: (newZoom: number) => void) => {
  return (e: WheelEvent) => {
    e.preventDefault();
    const zoomDelta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
    handleZoomChange(zoom + zoomDelta);
  };
};

/**
 * Создает обработчик клика для получения координат
 */
export const createClickHandler = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  setCellTarget: (cell: { isoX: number; isoY: number; tileData: any } | null) => void,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    const clickX = e.clientX - rect.left;
    const clickY = e.clientY - rect.top;

    const mapSize = currentWorld?.settings?.worldSize || 20;
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        // Получаем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const tileKey = `${isoX},${isoY}`;
          const tileData = currentWorld?.worldMap?.[tileKey];
          foundTile = { isoX, isoY, tileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Проверяем, кликнули ли мы на уже выбранный тайл
      if (cellTarget && cellTarget.isoX === foundTile.isoX && cellTarget.isoY === foundTile.isoY) {
        // Отменяем выбор
        setCellTarget(null);
        console.log('Tile selection cancelled');
      } else {
        // Выбираем новый тайл
        setCellTarget(foundTile);
      }
    }
  };
};

/**
 * Создает обработчик клавиш для управления камерой
 */
export const createKeyDownHandler = (cameraRef: React.RefObject<CameraRef>) => {
  return (e: KeyboardEvent) => {
    if (!cameraRef.current) return;

    switch (e.key) {
      case 'ArrowUp':
        cameraRef.current.y -= CAMERA_MOVE_SPEED;
        break;
      case 'ArrowDown':
        cameraRef.current.y += CAMERA_MOVE_SPEED;
        break;
      case 'ArrowLeft':
        cameraRef.current.x -= CAMERA_MOVE_SPEED;
        break;
      case 'ArrowRight':
        cameraRef.current.x += CAMERA_MOVE_SPEED;
        break;
      default:
        return;
    }
    e.preventDefault();
  };
};

/**
 * Создает обработчик для отключения контекстного меню
 */
export const createContextMenuHandler = () => {
  return (e: MouseEvent) => e.preventDefault();
};
