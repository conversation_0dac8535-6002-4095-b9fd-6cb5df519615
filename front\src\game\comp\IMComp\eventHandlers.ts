/**
 * Обработчики событий для изометрической карты
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { screenToIso } from './coordinateUtils';
import { CAMERA_MOVE_SPEED, MIN_ZOOM, MAX_ZOOM, ZOOM_STEP } from './constants';
// ...existing code...



export interface CameraRef {
  x: number;
  y: number;
}

/**
 * Создает обработчик движения мыши для перемещения камеры
 */
export const createMouseMoveHandler = (cameraRef: React.RefObject<CameraRef>) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    // Перетаскивание только правой кнопкой мыши (e.buttons === 2)
    if (e.buttons === 2 && cameraRef.current) {
      cameraRef.current.x -= e.movementX;
      cameraRef.current.y -= e.movementY;
    }
  };
};

/**
 * Создает обработчик изменения зума
 */
export const createZoomChangeHandler = (setZoom: (zoom: number) => void) => {
  return (newZoom: number) => {
    setZoom(Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, newZoom)));
  };
};

/**
 * Создает обработчик колеса мыши для зума
 */
export const createWheelHandler = (zoom: number, handleZoomChange: (newZoom: number) => void) => {
  return (e: WheelEvent) => {
    e.preventDefault();
    const zoomDelta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
    handleZoomChange(zoom + zoomDelta);
  };
};

/**
 * Создает обработчик клика для получения координат
 */
export const createClickHandler = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  setCellTarget: (cell: { isoX: number; isoY: number; tileData: any }) => void
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    const clickX = e.clientX - rect.left - canvasWidth / 2 + cameraRef.current.x;
    const clickY = e.clientY - rect.top - canvasHeight / 2 + cameraRef.current.y;

    const { x: isoX, y: isoY } = screenToIso(clickX, clickY, tileWidth, tileHeight);
    const mapSize = currentWorld?.settings?.worldSize || 20;

    if (isoX >= 0 && isoX < mapSize && isoY >= 0 && isoY < mapSize) {
      // Получаем данные тайла
      const tileKey = `${isoX},${isoY}`;
      const tileData = currentWorld?.worldMap?.[tileKey];
      setCellTarget({ isoX, isoY, tileData });
      // Здесь можно добавить логику обработки клика по тайлу
      console.log('Clicked tile:', { isoX, isoY, tileData });
    }
  };
};

/**
 * Создает обработчик клавиш для управления камерой
 */
export const createKeyDownHandler = (cameraRef: React.RefObject<CameraRef>) => {
  return (e: KeyboardEvent) => {
    if (!cameraRef.current) return;

    switch (e.key) {
      case 'ArrowUp':
        cameraRef.current.y -= CAMERA_MOVE_SPEED;
        break;
      case 'ArrowDown':
        cameraRef.current.y += CAMERA_MOVE_SPEED;
        break;
      case 'ArrowLeft':
        cameraRef.current.x -= CAMERA_MOVE_SPEED;
        break;
      case 'ArrowRight':
        cameraRef.current.x += CAMERA_MOVE_SPEED;
        break;
      default:
        return;
    }
    e.preventDefault();
  };
};

/**
 * Создает обработчик для отключения контекстного меню
 */
export const createContextMenuHandler = () => {
  return (e: MouseEvent) => e.preventDefault();
};
