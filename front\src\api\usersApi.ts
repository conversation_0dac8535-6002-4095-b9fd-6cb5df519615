import { authFetch } from '../utils/authFetch';

export interface UpdateProfileRequest {
  username: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileResponse {
  id: string;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ChangePasswordResponse {
  message: string;
}

const API_BASE_URL = import.meta.env.VITE_AUTH_API_URL || '/api';

export const usersApi = {
  async updateProfile(data: UpdateProfileRequest): Promise<UpdateProfileResponse> {
    const response = await authFetch(`${API_BASE_URL}/users/profile`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      
      throw new Error(`Failed to update profile: ${response.status} ${response.statusText}`);
    }

    const responseText = await response.text();
    
    
    if (!responseText) {
      throw new Error('Empty response from server');
    }
    
    try {
      return JSON.parse(responseText);
    } catch (e) {
     
      throw new Error('Invalid JSON response from server');
    }
  },

  async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    const response = await authFetch(`${API_BASE_URL}/users/password`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to change password');
    }

    return response.json();
  },
};
