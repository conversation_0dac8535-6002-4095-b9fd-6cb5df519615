// URL сервиса авторизации - используем nginx proxy
const AUTH_SERVICE_URL = '/api'

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    username: string
    role: string
  }
  token: string
}

export interface UserProfile {
  id: string
  email: string
  username: string
  role: string
  isActive: boolean
  createdAt: string
  lastLoginAt?: string
}

// Создание базового клиента с автоматическим добавлением токена
class AuthApiClient {
  private getHeaders(token?: string): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    return headers
  }

  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/login`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка авторизации: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/register`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка регистрации: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async getProfile(token: string): Promise<UserProfile> {
    
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/profile`, {
      method: 'GET',
      headers: this.getHeaders(token),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка получения профиля: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(`${AUTH_SERVICE_URL}/auth/verify`, {
        method: 'POST',
        headers: this.getHeaders(token),
      })

      const isValid = response.ok
      return isValid
    } catch (error) {
      return false
    }
  }
}

export const authApi = new AuthApiClient()
