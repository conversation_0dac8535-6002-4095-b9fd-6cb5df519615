import { useAuthStore } from '../store/authStore'

/**
 * Создаёт fetch с автоматическим добавлением токена авторизации
 */
export const createAuthenticatedFetch = () => {
  return async (url: string | URL | Request, options: RequestInit = {}): Promise<Response> => {
    const { token, isAuthenticated } = useAuthStore.getState()
    
    const headers = new Headers(options.headers)
    
    // Добавляем токен если он есть
    if (token && !headers.has('Authorization')) {
      headers.set('Authorization', `Bearer ${token}`)
    }
    
    // Добавляем Content-Type если не установлен
    if (!headers.has('Content-Type') && options.body) {
      headers.set('Content-Type', 'application/json')
    }

    const authenticatedOptions: RequestInit = {
      ...options,
      headers
    }

    const response = await fetch(url, authenticatedOptions)
    
    // Если получили 401, значит токен истёк - очищаем авторизацию
    if (response.status === 401) {
      const { logout } = useAuthStore.getState()
      logout()
    }
    
    return response
  }
}

// Экземпляр для использования в приложении
export const authFetch = createAuthenticatedFetch()

/**
 * Хелпер для создания API клиентов с автоматической авторизацией
 */
export class AuthApiClient {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // Убираем слэш в конце
  }

  private getFullUrl(endpoint: string): string {
    return `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
  }

  async get<T>(endpoint: string): Promise<T> {
    const fullUrl = this.getFullUrl(endpoint)
    
    const response = await authFetch(fullUrl, {
      method: 'GET'
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`GET ${endpoint}: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const result = await response.json()
    return result
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await authFetch(this.getFullUrl(endpoint), {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    })

    if (!response.ok) {
      throw new Error(`POST ${endpoint}: ${response.statusText}`)
    }

    return response.json()
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await authFetch(this.getFullUrl(endpoint), {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    })

    if (!response.ok) {
      throw new Error(`PUT ${endpoint}: ${response.statusText}`)
    }

    return response.json()
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await authFetch(this.getFullUrl(endpoint), {
      method: 'DELETE'
    })

    if (!response.ok) {
      throw new Error(`DELETE ${endpoint}: ${response.statusText}`)
    }

    return response.json()
  }
}
