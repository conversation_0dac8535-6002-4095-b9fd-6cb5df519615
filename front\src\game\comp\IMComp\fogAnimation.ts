/**
 * Система анимации тумана войны
 * Обеспечивает плавные переходы между различными состояниями тумана с случайным мерцанием
 */

import { FOG_ANIMATION_SPEED, FOG_TRANSITION_DURATION, FOG_FLICKER_INTENSITY } from './constants'

/**
 * Состояние анимации для одного тайла тумана
 */
export interface FogTileAnimationState {
  currentImageIndex: number // Текущее изображение (0-5)
  targetImageIndex: number // Целевое изображение (0-5)
  transitionProgress: number // Прогресс перехода (0-1)
  transitionStartTime: number // Время начала перехода
  nextTransitionTime: number // Время следующего перехода
  flickerOffset: number // Смещение для мерцания
  flickerPhase: number // Фаза мерцания
}

/**
 * Глобальное состояние анимации тумана войны
 */
export class FogAnimationManager {
  private tileStates: Map<string, FogTileAnimationState> = new Map()
  private lastUpdateTime: number = 0
  private globalTime: number = 0
  private opacityCache: Map<string, number[]> = new Map()
  private lastCacheUpdate: number = 0
  private readonly CACHE_UPDATE_INTERVAL = 16 // Обновляем кэш каждые 16мс (60 FPS)

  /**
   * Инициализирует состояние анимации для тайла
   */
  initializeTile(tileKey: string, initialVariant: number): void {
    if (this.tileStates.has(tileKey)) return

    // Используем координаты тайла для создания уникального seed
    const [x, y] = tileKey.split(',').map(Number)
    const seed = this.createSeed(x, y)
    
    const state: FogTileAnimationState = {
      currentImageIndex: Math.max(0, Math.min(5, initialVariant - 1)),
      targetImageIndex: Math.max(0, Math.min(5, initialVariant - 1)),
      transitionProgress: 1,
      transitionStartTime: 0,
      nextTransitionTime: this.globalTime + this.getRandomTransitionDelay(seed),
      flickerOffset: this.getRandomFlickerOffset(seed),
      flickerPhase: this.getRandomPhase(seed)
    }

    this.tileStates.set(tileKey, state)
  }

  /**
   * Обновляет анимацию для всех тайлов
   */
  update(currentTime: number): void {
    const deltaTime = currentTime - this.lastUpdateTime
    this.globalTime += deltaTime
    this.lastUpdateTime = currentTime

    for (const [tileKey, state] of this.tileStates) {
      this.updateTileState(tileKey, state, currentTime)
    }
  }

  /**
   * Получает текущее состояние анимации для тайла
   */
  getTileAnimationState(tileKey: string): FogTileAnimationState | null {
    return this.tileStates.get(tileKey) || null
  }

  /**
   * Вычисляет интерполированную прозрачность между двумя изображениями (с кэшированием)
   */
  getInterpolatedOpacity(state: FogTileAnimationState, imageIndex: number): number {
    if (state.transitionProgress >= 1) {
      // Переход завершен
      return imageIndex === state.currentImageIndex ? 1 : 0
    }

    // Во время перехода
    const baseOpacity = this.calculateBaseOpacity(state, imageIndex)
    const flickerMultiplier = this.calculateFlickerMultiplier(state)

    return Math.max(0, Math.min(1, baseOpacity * flickerMultiplier))
  }

  /**
   * Получает кэшированные значения прозрачности для тайла
   */
  getCachedOpacities(tileKey: string): number[] | null {
    const currentTime = this.globalTime
    if (currentTime - this.lastCacheUpdate < this.CACHE_UPDATE_INTERVAL) {
      return this.opacityCache.get(tileKey) || null
    }
    return null
  }

  /**
   * Обновляет кэш прозрачности для тайла
   */
  updateOpacityCache(tileKey: string, opacities: number[]): void {
    this.opacityCache.set(tileKey, [...opacities])
    this.lastCacheUpdate = this.globalTime
  }

  /**
   * Обновляет состояние конкретного тайла
   */
  private updateTileState(tileKey: string, state: FogTileAnimationState, currentTime: number): void {
    // Обновляем фазу мерцания
    state.flickerPhase += FOG_ANIMATION_SPEED

    // Проверяем, нужно ли начать новый переход
    if (currentTime >= state.nextTransitionTime && state.transitionProgress >= 1) {
      this.startNewTransition(tileKey, state, currentTime)
    }

    // Обновляем прогресс текущего перехода
    if (state.transitionProgress < 1) {
      const elapsed = currentTime - state.transitionStartTime
      state.transitionProgress = Math.min(1, elapsed / FOG_TRANSITION_DURATION)

      // Если переход завершен, обновляем текущее изображение
      if (state.transitionProgress >= 1) {
        state.currentImageIndex = state.targetImageIndex
      }
    }
  }

  /**
   * Начинает новый переход для тайла
   */
  private startNewTransition(tileKey: string, state: FogTileAnimationState, currentTime: number): void {
    const [x, y] = tileKey.split(',').map(Number)
    const seed = this.createSeed(x, y, this.globalTime)

    // Выбираем новое целевое изображение (отличное от текущего)
    const availableImages = [0, 1, 2, 3, 4, 5].filter(i => i !== state.currentImageIndex)
    state.targetImageIndex = availableImages[Math.floor(this.pseudoRandom(seed) * availableImages.length)]

    // Сбрасываем прогресс перехода
    state.transitionProgress = 0
    state.transitionStartTime = currentTime

    // Планируем следующий переход
    state.nextTransitionTime = currentTime + this.getRandomTransitionDelay(seed + 1)
  }

  /**
   * Вычисляет базовую прозрачность для изображения во время перехода
   */
  private calculateBaseOpacity(state: FogTileAnimationState, imageIndex: number): number {
    if (imageIndex === state.currentImageIndex) {
      return 1 - state.transitionProgress
    } else if (imageIndex === state.targetImageIndex) {
      return state.transitionProgress
    }
    return 0
  }

  /**
   * Вычисляет множитель мерцания
   */
  private calculateFlickerMultiplier(state: FogTileAnimationState): number {
    const flicker = Math.sin(state.flickerPhase + state.flickerOffset) * FOG_FLICKER_INTENSITY
    return 1 + flicker
  }

  /**
   * Создает seed на основе координат и времени
   */
  private createSeed(x: number, y: number, time: number = 0): number {
    return ((x * 73856093) ^ (y * 19349663) ^ (time * 83492791)) >>> 0
  }

  /**
   * Генерирует псевдослучайное число от 0 до 1
   */
  private pseudoRandom(seed: number): number {
    seed = (seed * 9301 + 49297) % 233280
    return seed / 233280
  }

  /**
   * Получает случайную задержку для следующего перехода
   */
  private getRandomTransitionDelay(seed: number): number {
    const minDelay = FOG_TRANSITION_DURATION * 0.5
    const maxDelay = FOG_TRANSITION_DURATION * 2
    return minDelay + this.pseudoRandom(seed) * (maxDelay - minDelay)
  }

  /**
   * Получает случайное смещение для мерцания
   */
  private getRandomFlickerOffset(seed: number): number {
    return this.pseudoRandom(seed) * Math.PI * 2
  }

  /**
   * Получает случайную фазу
   */
  private getRandomPhase(seed: number): number {
    return this.pseudoRandom(seed) * Math.PI * 2
  }

  /**
   * Очищает состояние для тайлов, которые больше не видны
   */
  cleanup(visibleTileKeys: Set<string>): void {
    // Очищаем состояния тайлов
    for (const tileKey of this.tileStates.keys()) {
      if (!visibleTileKeys.has(tileKey)) {
        this.tileStates.delete(tileKey)
      }
    }

    // Очищаем кэш прозрачности
    for (const tileKey of this.opacityCache.keys()) {
      if (!visibleTileKeys.has(tileKey)) {
        this.opacityCache.delete(tileKey)
      }
    }
  }

  /**
   * Получает статистику производительности
   */
  getPerformanceStats(): { activeTiles: number; cacheSize: number } {
    return {
      activeTiles: this.tileStates.size,
      cacheSize: this.opacityCache.size
    }
  }
}

// Глобальный экземпляр менеджера анимации
export const fogAnimationManager = new FogAnimationManager()
