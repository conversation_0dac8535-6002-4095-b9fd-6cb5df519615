/**
 * Утилиты для работы с координатами изометрической карты
 */

/**
 * Функция преобразования изометрических координат в экранные
 */
export const isoToScreen = (isoX: number, isoY: number, tileWidth: number, tileHeight: number) => {
  const screenX = (isoX - isoY) * (tileWidth / 2)
  const screenY = (isoX + isoY) * (tileHeight / 2)
  return { x: screenX, y: screenY }
}

/**
 * Функция преобразования экранных координат в изометрические
 */
export const screenToIso = (screenX: number, screenY: number, tileWidth: number, tileHeight: number) => {
  const isoX = (screenX / (tileWidth / 2) + screenY / (tileHeight / 2)) / 2
  const isoY = (screenY / (tileHeight / 2) - screenX / (tileWidth / 2)) / 2
  return { x: Math.floor(isoX), y: Math.floor(isoY) }
}

/**
 * Вычисляет масштабированные размеры тайлов (всегда чётные)
 */
export const getScaledTileSize = (baseTileWidth: number, baseTileHeight: number, zoom: number) => {
  const tileWidth = Math.round(baseTileWidth * zoom / 2) * 2
  const tileHeight = Math.round(baseTileHeight * zoom / 2) * 2
  return { tileWidth, tileHeight }
}

/**
 * Проверяет, находится ли тайл в видимой области
 */
export const isTileVisible = (
  centerX: number, 
  centerY: number, 
  tileWidth: number, 
  tileHeight: number, 
  canvasWidth: number, 
  canvasHeight: number
) => {
  return !(
    centerX < -tileWidth || 
    centerX > canvasWidth + tileWidth ||
    centerY < -tileHeight || 
    centerY > canvasHeight + tileHeight
  )
}

/**
 * Вычисляет центральные координаты тайла на экране
 */
export const getTileCenterOnScreen = (
  screenX: number, 
  screenY: number, 
  canvasWidth: number, 
  canvasHeight: number, 
  cameraX: number, 
  cameraY: number
) => {
  const centerX = screenX + canvasWidth / 2 - cameraX
  const centerY = screenY + canvasHeight / 2 - cameraY
  return { centerX, centerY }
}
