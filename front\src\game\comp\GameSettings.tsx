import React, { useState } from 'react'
import { X, Volume2, Monitor, <PERSON>, <PERSON>, Save } from 'lucide-react'
import { useGameStore } from '../../store/gameStore'
import styles from './GameSettings.module.css'

interface GameSettingsProps {
  onClose: () => void
}

const GameSettings: React.FC<GameSettingsProps> = ({ onClose }) => {
  const { settings, updateSettings } = useGameStore()
  
  // Локальное состояние для настроек
  const [localSettings, setLocalSettings] = useState({
    volume: settings.volume,
    soundEnabled: settings.soundEnabled,
    musicEnabled: settings.musicEnabled,
    brightness: 0.8, // Новая настройка яркости
    contrast: 1.0,   // Новая настройка контрастности
  })

  const handleSettingChange = (key: string, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = () => {
    // Сохраняем настройки в store
    updateSettings({
      volume: localSettings.volume,
      soundEnabled: localSettings.soundEnabled,
      musicEnabled: localSettings.musicEnabled,
    })

    // Применяем настройки дисплея
    applyDisplaySettings()

    onClose()
  }

  const applyDisplaySettings = () => {
    // Применяем яркость и контрастность к игровому интерфейсу
    const gameArea = document.querySelector('.gameArea')
    if (gameArea) {
      (gameArea as HTMLElement).style.filter = 
        `brightness(${localSettings.brightness}) contrast(${localSettings.contrast})`
    }
  }

  const resetToDefaults = () => {
    setLocalSettings({
      volume: 0.7,
      soundEnabled: true,
      musicEnabled: true,
      brightness: 0.8,
      contrast: 1.0,
    })
  }

  return (
    <div className={styles.gameSettings}>
      <div className={styles.header}>
        <h2>Настройки игры</h2>
        <button className={styles.closeButton} onClick={onClose}>
          <X />
        </button>
      </div>

      <div className={styles.content}>
        {/* Аудио настройки */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            <Volume2 />
            Аудио
          </h3>
          
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Общая громкость: {Math.round(localSettings.volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={localSettings.volume}
              onChange={(e) => handleSettingChange('volume', parseFloat(e.target.value))}
              className={styles.slider}
            />
          </div>

          <div className={styles.setting}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={localSettings.soundEnabled}
                onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}
              />
              <span>Звуковые эффекты</span>
            </label>
          </div>

          <div className={styles.setting}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={localSettings.musicEnabled}
                onChange={(e) => handleSettingChange('musicEnabled', e.target.checked)}
              />
              <span>Фоновая музыка</span>
            </label>
          </div>
        </div>

        {/* Видео настройки */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            <Monitor />
            Видео
          </h3>
          
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              <Sun />
              Яркость: {Math.round(localSettings.brightness * 100)}%
            </label>
            <input
              type="range"
              min="0.3"
              max="1.5"
              step="0.1"
              value={localSettings.brightness}
              onChange={(e) => handleSettingChange('brightness', parseFloat(e.target.value))}
              className={styles.slider}
            />
          </div>

          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              <Moon />
              Контрастность: {Math.round(localSettings.contrast * 100)}%
            </label>
            <input
              type="range"
              min="0.5"
              max="2.0"
              step="0.1"
              value={localSettings.contrast}
              onChange={(e) => handleSettingChange('contrast', parseFloat(e.target.value))}
              className={styles.slider}
            />
          </div>


        </div>


      </div>

      <div className={styles.footer}>
        <button className={styles.resetButton} onClick={resetToDefaults}>
          Сбросить
        </button>
        <div className={styles.actionButtons}>
          <button className={styles.cancelButton} onClick={onClose}>
            Отмена
          </button>
          <button className={styles.saveButton} onClick={handleSave}>
            <Save />
            Сохранить
          </button>
        </div>
      </div>
    </div>
  )
}

export default GameSettings
