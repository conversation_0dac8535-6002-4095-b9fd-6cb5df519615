/* Основные стили игровой страницы с темной постапокалиптической тематикой */
:root {
  --primary-color: #94c47d;
  --primary-dark: #6b8f5c;
  --bg-primary: #0f0f0f;
  --bg-secondary: #1e1e1e;
  --bg-overlay: rgba(15, 15, 15, 0.85);
  --text-primary: #b7e49d;
  --text-secondary: #9bb891;
  --text-muted: #667766;
  --border-color: #334433;
  --border-light: #556655;
  --shadow-primary: rgba(148, 196, 125, 0.25);
  --shadow-glow: rgba(180, 255, 140, 0.35);
}

/* :root {
  --primary-color: #ff6b35;
  --primary-dark: #e55a2b;
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --text-primary: #e0e0e0;
  --text-secondary: #bbb;
  --text-muted: #888;
  --border-color: #444;
  --border-light: #666;
  --shadow-primary: rgba(255, 107, 53, 0.3);
  --shadow-glow: rgba(255, 107, 53, 0.5);
} */

.gamePage {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
  color: var(--text-primary);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  position: relative;
}

.gamePage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(150, 255, 120, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(100, 255, 180, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(80, 120, 80, 0.06) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.gameInterface {
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Верхняя панель */
.topBar {
  background: var(--bg-overlay);
  border-bottom: 2px solid var(--border-color);
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.worldInfo h1 {
  margin: 0;
  font-size: 24px;
  color: var(--primary-color);
  text-shadow: 0 0 10px var(--shadow-primary);
  font-weight: 700;
}

.worldDescription {
  color: var(--text-secondary);
  font-size: 14px;
  margin-top: 4px;
}

.gameControls {
  display: flex;
  gap: 8px;
}

.controlButton {
  background: rgba(33, 66, 5, 0.1);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.controlButton:hover {
  background: var(--primary-dark);  
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: translateY(-1px);
}

.controlButton:active {
  transform: translateY(0);
}

/* Основная игровая область */
.gameArea {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gameContent {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
 
  border-radius: 12px;
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder {
  text-align: center;
  padding: 40px;
}

.placeholderIcon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.placeholder h2 {
  color: var(--primary-color);
  margin-bottom: 16px;
  font-size: 28px;
  text-shadow: 0 0 10px var(--shadow-primary);
}

.placeholder p {
  color: var(--text-secondary);
  font-size: 16px;
  margin-bottom: 30px;
}

.gameStats {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-top: 20px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.statLabel {
  color: var(--text-muted);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.statValue {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 600;
}

/* Модальные окна */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modalContent {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.dialogHeader {
  padding: 20px 20px 0;
  border-bottom: 1px solid var(--border-color);
}

.dialogHeader h3 {
  margin: 0 0 20px 0;
  color: var(--primary-color);
  font-size: 20px;
  text-shadow: 0 0 10px var(--shadow-primary);
}

.dialogBody {
  padding: 20px;
}

.dialogBody p {
  margin: 0 0 16px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.warning {
  color: var(--primary-color) !important;
  font-weight: 600;
  text-shadow: 0 0 5px var(--shadow-primary);
}

.saveOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.saveButton {
  background: rgba(33, 66, 5, 0.1);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.saveButton:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
}

.dialogActions {
  padding: 0 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirmButton {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.confirmButton:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
}

.cancelButton {
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--text-muted);
}

/* Загрузка */
.loading {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-primary);
}

.loadingContent {
  text-align: center;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingContent p {
  color: var(--text-secondary);
  font-size: 16px;
}