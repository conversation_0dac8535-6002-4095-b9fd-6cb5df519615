/**
 * Компоненты управления для изометрической карты
 */

import * as React from 'react'
import styles from '../IsometricMap.module.css'
import { MAX_ZOOM, MIN_ZOOM } from './constants';
import monitorWrapper from '../../../../public/textures/gameMenu/infoTerminal.png'

interface MapControlsProps {
  displayCoordinates: { x: number; y: number }
}
export const DisplayInfoWrapper: React.FC<MapControlsProps> = () => {
  return (
    <div className={styles.monitorWrapper}>
      {monitorWrapper}
    </div>
  )
}
/**
 * Компонент отображения информации о камере и инструкций
 */
export const MapControls: React.FC<MapControlsProps> = ({ displayCoordinates }) => {
  return (
    <div className={styles.mapControls}>
      <div className={styles.coordinates}>
        Камера: ({displayCoordinates.x}, {displayCoordinates.y})
      </div>
      <div className={styles.instructions}>
        ПКМ - перетаскивание, стрелки - движение, колесо - зум
      </div>
    </div>
  )
}

interface ZoomControlsProps {
  zoom: number
  onZoomChange: (newZoom: number) => void
}

/**
 * Компонент управления зумом
 */
export const ZoomControls: React.FC<ZoomControlsProps> = ({ zoom, onZoomChange }) => {
  return (
    <div className={styles.zoomControls}>
      <label className={styles.zoomLabel}>
        Зум: {zoom.toFixed(1)}x
      </label>
      <input
        type="range"
        min={MIN_ZOOM}
        max={MAX_ZOOM}
        step="0.1"
        value={zoom}
        onChange={(e) => onZoomChange(parseFloat(e.target.value))}
        className={styles.zoomSlider}
      />
    </div>
  )
}
