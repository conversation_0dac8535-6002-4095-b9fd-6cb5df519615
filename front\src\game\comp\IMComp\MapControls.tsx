/**
 * Компоненты управления для изометрической карты
 */

import * as React from 'react'
import styles from '../IsometricMap.module.css'
import { MAX_ZOOM, MIN_ZOOM } from './constants';

interface MapControlsProps {
  cameraUI: { x: number; y: number }
}

/**
 * Компонент отображения информации о камере и инструкций
 */
export const MapControls: React.FC<MapControlsProps> = ({ cameraUI }) => {
  return (
    <div className={styles.mapControls}>
      <div className={styles.coordinates}>
        Камера: ({cameraUI.x}, {cameraUI.y})
      </div>
      <div className={styles.instructions}>
        ПКМ - перетаскивание, стрелки - движение, колесо - зум
      </div>
    </div>
  )
}

interface ZoomControlsProps {
  zoom: number
  onZoomChange: (newZoom: number) => void
}

/**
 * Компонент управления зумом
 */
export const ZoomControls: React.FC<ZoomControlsProps> = ({ zoom, onZoomChange }) => {
  return (
    <div className={styles.zoomControls}>
      <label className={styles.zoomLabel}>
        Зум: {zoom.toFixed(1)}x
      </label>
      <input
        type="range"
        min={MIN_ZOOM}
        max={MAX_ZOOM}
        step="0.1"
        value={zoom}
        onChange={(e) => onZoomChange(parseFloat(e.target.value))}
        className={styles.zoomSlider}
      />
    </div>
  )
}
