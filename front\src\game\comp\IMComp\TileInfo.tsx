/**
 * Компонент для отображения информации о выбранном тайле
 */

import * as React from 'react'
import { TerrainType } from '../../../shared/enums'
import { WorldMapCell } from '../../../shared/types/World'
import styles from './TileInfo.module.css'

interface TileInfoProps {
  cellTarget: { isoX: number; isoY: number; tileData: WorldMapCell } | null
  onClose: () => void
}

/**
 * Получает описание местности на русском языке
 */
const getTerrainDescription = (terrain: TerrainType): { name: string; description: string } => {
  switch (terrain) {
    case TerrainType.GRASS:
      return {
        name: 'Луга',
        description: 'Зеленые травянистые равнины, богатые жизнью'
      }
    case TerrainType.DEADFOREST:
      return {
        name: 'Мертвый лес',
        description: 'Выжженные радиацией остатки некогда зеленого леса'
      }
    case TerrainType.MOUNTAIN:
      return {
        name: 'Горы',
        description: 'Каменистые возвышенности, труднопроходимые, но богатые ресурсами'
      }
    case TerrainType.WATER:
      return {
        name: 'Водоем',
        description: 'Источник чистой воды, жизненно важный ресурс'
      }
    case TerrainType.DESERT:
      return {
        name: 'Пустыня',
        description: 'Выжженные солнцем песчаные дюны'
      }
    case TerrainType.SWAMP:
      return {
        name: 'Болото',
        description: 'Топкие земли, полные опасностей и мутантов'
      }
    case TerrainType.WASTELAND:
      return {
        name: 'Пустошь',
        description: 'Выжженные солнцем пустоши, оставшиеся после ядерной войны'
      }
    case TerrainType.ROAD:
      return {
        name: 'Дорога',
        description: 'Старая асфальтированная дорога довоенных времен'
      }
    case TerrainType.CITY:
      return {
        name: 'Город',
        description: 'Руины некогда великого города'
      }
    case TerrainType.RUINS:
      return {
        name: 'Руины',
        description: 'Разрушенные здания и сооружения прошлого'
      }
    default:
      return {
        name: 'Неизвестная местность',
        description: 'Загадочная территория'
      }
  }
}

/**
 * Компонент всплывающего окна с информацией о тайле
 */
export const TileInfo: React.FC<TileInfoProps> = ({ cellTarget, onClose }) => {
  if (!cellTarget || !cellTarget.tileData) {
    return null
  }

  const { isoX, isoY, tileData } = cellTarget
  const terrainInfo = getTerrainDescription(tileData.terrain)

  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.tileInfo} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <h3 className={styles.title}>{terrainInfo.name}</h3>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>
        
        <div className={styles.content}>
          <p className={styles.description}>{terrainInfo.description}</p>
          
          <div className={styles.details}>
            <div className={styles.coordinate}>
              <span className={styles.label}>Координаты:</span>
              <span className={styles.value}>({isoX}, {isoY})</span>
            </div>
            
            {tileData.blocked && (
              <div className={styles.warning}>
                ⚠️ Проход заблокирован
              </div>
            )}
            
            {tileData.location && (
              <div className={styles.location}>
                📍 Локация: {tileData.location.name || 'Неизвестная локация'}
              </div>
            )}
            
            {tileData.fogOfWar && (
              <div className={styles.fog}>
                🌫️ Покрыто туманом войны
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
