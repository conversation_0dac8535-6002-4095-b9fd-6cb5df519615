
# Быстрый старт: <PERSON><PERSON><PERSON><PERSON> (IsometricMap Components)

Модуль для изометрической карты. Всё разбито по категориям — ищи нужное по названию файла:

## Категории

- **constants.ts** — константы (размеры тайлов, FPS, камера, ресурсы, текстуры местности)
- **coordinateUtils.ts** — координаты (iso/screen, tile size, tile visibility)
- **renderUtils.ts** — отрисовка (цвет тайла, тайл, туман войны, **текстуры местности**)
- **eventHandlers.ts** — события (мы<PERSON><PERSON>, zoom, колесо, кли<PERSON>и, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, контекстное меню)
- **hooks.ts** — хуки (туман войны, центр камеры, рендер-цикл, UI камеры, управление, **предзагрузка текстур**)
- **drawEngine.ts** — движок рендера (функция отрисовки карты)
- **MapControls.tsx** — UI-компоненты (инфо камеры, управление зумом)
- **index.ts** — экспорт всего

## Импорт

```typescript
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  getScaledTileSize,
  useFogOfWarImage,
  useTerrainTextures,
  useCameraCenter,
  useRenderLoop,
  createDrawFunction,
  drawTerrainTexture,
  terrainTextureManager,
  MapControls,
  ZoomControls
} from './IMComp'
```

## Работа с текстурами местности

### Простое использование (автоматически)
Текстуры местности отрисовываются автоматически в функции `drawTile()` на основе `tileData.terrain`.

### Ручное использование
```typescript
// Предзагрузка текстур (в компоненте)
const { texturesLoaded } = useTerrainTextures()

// Отрисовка текстуры на ромбовидный тайл
drawTerrainTexture(
  ctx,                    // Контекст канваса
  centerX, centerY,       // Центр тайла
  halfTileW, halfTileH,   // Половины размеров тайла
  TerrainType.GRASS,      // Тип местности
  0,                      // Вариация текстуры (0-3, опционально)
  Math.PI / 4             // Поворот в радианах (опционально)
)

// Получение загруженных текстур
const grassTextures = terrainTextureManager.getLoadedTextures(TerrainType.GRASS)
```

### Доступные типы местности
- `GRASS` - трава (4 вариации)
- `DEADFOREST` - мертвый лес (4 вариации)
- `MOUNTAIN` - горы (4 вариации)
- `WATER` - вода (4 вариации)
- `DESERT` - пустыня (4 вариации)
- `SWAMP` - болото (4 вариации)
- `WASTELAND` - пустошь (4 вариации)

## Преимущества

- Модульность: всё по категориям
- Переиспользование: утилиты и хуки можно брать в других компонентах
- Тестируемость: каждый модуль тестируется отдельно
- Читаемость: основной файл стал короче
- Поддерживаемость: изменения локализованы

## 🎨 Система текстур местности

### Особенности
- **Автоматическая загрузка**: Текстуры загружаются асинхронно и кэшируются
- **Простота использования**: Одна функция для отрисовки любой текстуры на ромб
- **Вариации**: Каждый тип местности имеет 4 вариации текстур
- **Поворот**: Поддержка поворота текстур
- **Fallback**: Автоматический откат к цветной заливке если текстуры не загружены

### Быстрый старт
```typescript
// 1. Импортируем нужные функции
import { useTerrainTextures, drawTerrainTexture, TerrainType } from './IMComp'

// 2. В компоненте предзагружаем текстуры
const { texturesLoaded } = useTerrainTextures()

// 3. Отрисовываем текстуру на ромб
drawTerrainTexture(ctx, centerX, centerY, halfWidth, halfHeight, TerrainType.GRASS)
```

### Демонстрационные функции
```typescript
import { drawTerrainDemo, logTextureStatus } from './IMComp'

// Отрисовать все типы местности для тестирования
drawTerrainDemo(ctx)

// Проверить статус загрузки в консоли
logTextureStatus()
```
