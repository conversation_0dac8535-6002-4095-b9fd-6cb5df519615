.settingsPanel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid #4b5563;
  margin-bottom: 1rem;
}

.tab {
  background: none;
  border: none;
  color: #9ca3af;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.05);
}

.tabActive {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 0.5rem;
}

.setting {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.settingRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settingLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settingLabel svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #9ca3af;
}

.labelText {
  color: white;
  font-weight: 500;
}

.labelDescription {
  font-size: 0.875rem;
  color: #9ca3af;
}

.volumeContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.volumeValue {
  color: #d1d5db;
}

.sliderContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sliderContainer svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #9ca3af;
}

.slider {
  flex: 1;
  height: 0.5rem;
  background-color: #374151;
  border-radius: 0.5rem;
  appearance: none;
  cursor: pointer;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.toggleOff {
  background-color: #4b5563;
}

.toggleOn {
  background-color: #16a34a;
}

.toggleThumb {
  display: inline-block;
  height: 1rem;
  width: 1rem;
  transform: translateX(0.25rem);
  border-radius: 50%;
  background-color: white;
  transition: transform 0.3s ease;
}

.toggleThumbOn {
  transform: translateX(1.5rem);
}

.difficultyGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.difficultyButton {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.difficultyButtonInactive {
  background-color: #374151;
  color: #d1d5db;
}

.difficultyButtonInactive:hover {
  background-color: #4b5563;
}

.difficultyButtonActive {
  background-color: #3b82f6;
  color: white;
}

.difficultyDescription {
  font-size: 0.875rem;
  color: #9ca3af;
}

.resetSection {
  padding-top: 1.5rem;
  border-top: 1px solid #4b5563;
}

.resetButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background-color: #374151;
  color: white;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.resetButton:hover {
  background-color: #4b5563;
}

.resetButton svg {
  width: 1rem;
  height: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .settingRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .volumeContainer {
    width: 100%;
  }
  
  .sliderContainer {
    width: 100%;
  }
  
  .difficultyGrid {
    grid-template-columns: 1fr;
  }
}

/* Account Settings Styles */
.gameSettings,
.accountSettings {
  max-width: 500px;
}

.accountForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inputGroup label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #d1d5db;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.inputIcon {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 0.5rem;
  color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.1);
}

.input::placeholder {
  color: #9ca3af;
}

.saveButton {
  padding: 0.75rem 1.5rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  align-self: flex-start;
}

.saveButton:hover {
  background: #1d4ed8;
}

.saveButton:active {
  transform: translateY(1px);
}

/* Better structure for account settings */
.profileSection,
.passwordSection {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid #374151;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.profileSection h3,
.passwordSection h3 {
  margin: 0 0 1rem 0;
  color: #e5e7eb;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profileSection .saveButton,
.passwordSection .saveButton {
  margin-top: 1rem;
}

/* Form improvements */
.inputGroup label {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  color: #d1d5db;
  font-weight: 500;
}

.inputWrapper {
  position: relative;
}

.inputIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 1;
  width: 18px;
  height: 18px;
}

.input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.75rem;
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.5rem;
  color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: #111827;
}

.input::placeholder {
  color: #6b7280;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .accountSettings {
    max-width: 100%;
  }
  
  .profileSection,
  .passwordSection {
    padding: 1rem;
  }
  
  .saveButton {
    width: 100%;
    justify-content: center;
  }
}
