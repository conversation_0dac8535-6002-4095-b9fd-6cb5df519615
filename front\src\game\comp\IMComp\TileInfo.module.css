/* Всплывающее окно с информацией о тайле */

.overlay {
  position: fixed;
  top: 0;
  left: 10;
  right: 0;
  bottom: 10;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.tileInfo {
  background: var(--bg-primary, #1a1a1a);
  border: 2px solid var(--border-color, #444);
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
  animation: slideIn 0.3s ease-out;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--border-color, #444);
  padding-bottom: 12px;
}

.title {
  color: var(--text-primary, #ffffff);
  font-size: 1.4em;
  font-weight: bold;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  color: var(--text-secondary, #aaa);
  font-size: 1.5em;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: var(--bg-secondary, #333);
  color: var(--text-primary, #fff);
}

.content {
  color: var(--text-primary, #ffffff);
}

.description {
  font-size: 1em;
  line-height: 1.4;
  margin-bottom: 16px;
  color: var(--text-secondary, #ccc);
}

.details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.coordinate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 4px;
}

.label {
  font-weight: 500;
  color: var(--text-secondary, #aaa);
}

.value {
  font-family: monospace;
  color: var(--accent-color, #00ff88);
  font-weight: bold;
}

.warning {
  padding: 8px 12px;
  background: rgba(255, 69, 0, 0.2);
  border: 1px solid rgba(255, 69, 0, 0.5);
  border-radius: 4px;
  color: #ff6b47;
  font-weight: 500;
}

.location {
  padding: 8px 12px;
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 4px;
  color: #ffd700;
  font-weight: 500;
}

.fog {
  padding: 8px 12px;
  background: rgba(128, 128, 128, 0.2);
  border: 1px solid rgba(128, 128, 128, 0.5);
  border-radius: 4px;
  color: #999;
  font-weight: 500;
}

/* Анимации */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Адаптивность */
@media (max-width: 480px) {
  .tileInfo {
    margin: 20px;
    min-width: auto;
    max-width: none;
  }
}
