/* Стили для компонента настроек игры */

.gameSettings {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-overlay);
}

.header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 22px;
  text-shadow: 0 0 10px var(--shadow-primary);
}

.closeButton {
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--text-muted);
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.section {
  margin-bottom: 30px;
}

.section:last-child {
  margin-bottom: 0;
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 0 5px var(--shadow-primary);
}

.setting {
  margin-bottom: 16px;
}

.settingLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 0 10px var(--shadow-primary);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px var(--shadow-primary);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: scale(1.1);
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 14px;
  transition: color 0.3s ease;
}

.checkboxLabel:hover {
  color: var(--primary-color);
}

.checkboxLabel input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

.select {
  width: 100%;
  padding: 8px 12px;
  background: var(--bg-overlay);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px var(--shadow-primary);
}

.select option {
  background: var(--bg-overlay);
  color: var(--text-primary);
}

.footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-overlay);
}

.resetButton {
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.resetButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--text-muted);
  color: var(--text-primary);
}

.actionButtons {
  display: flex;
  gap: 12px;
}

.cancelButton {
   background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #888;
  color: #fff;
}

.saveButton {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saveButton:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: translateY(-1px);
}

.saveButton:active {
  transform: translateY(0);
}

/* Скроллбар для контента */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: #333;
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Адаптивность */
@media (max-width: 768px) {
  .gameSettings {
    max-width: 95%;
    margin: 10px;
  }
  
  .header {
    padding: 15px;
  }
  
  .content {
    padding: 15px;
  }
  
  .footer {
    padding: 15px;
    flex-direction: column;
    gap: 15px;
  }
  
  .actionButtons {
    width: 100%;
    justify-content: center;
  }
  
  .resetButton {
    align-self: flex-start;
  }
}
