import { useEffect } from 'react'
import { useAuthStore } from '../store/authStore'

/**
 * Хук для автоматической инициализации авторизации
 * Проверяет сохранённый токен при загрузке приложения
 */
export const useAuthInit = () => {
  const checkAuth = useAuthStore(state => state.checkAuth)
  const isLoading = useAuthStore(state => state.isLoading)
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)

  useEffect(() => {
    // Проверяем авторизацию только один раз при загрузке
    checkAuth()
  }, [checkAuth])

  return {
    isLoading,
    isAuthenticated,
    isInitialized: !isLoading
  }
}
